﻿"use strict";
const webpack = require("webpack");
const { merge } = require('webpack-merge');
const common = require('./webpack.common');
const miniCssExtractPlugin = require("mini-css-extract-plugin");
const VueLoaderPlugin = require('vue-loader');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = merge(common, {
    mode: 'development',
    devtool: 'source-map',
    module: {
        rules: [
            {
                test: /\.vue$/,
                loader: 'vue-loader'
            },
            {
                test: /\.(js)$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: ['@babel/preset-env'],
                        plugins: [
                            [
                                "@babel/plugin-transform-runtime",
                                {
                                    "corejs": false,
                                    "helpers": false,
                                    "regenerator": true
                                }
                            ]
                        ]
                    }
                }
            },
            {
                test: /\.(sa|sc|c)ss$/,
                use: [
                    miniCssExtractPlugin.loader,
                    'css-loader',
                    'postcss-loader',
                    'sass-loader'
                ]
            },
            {
                test: /\.(jpe?g|png|gif|svg|eot|ttf|woff|woff2|otf|webp)$/,
                type: 'asset',
                generator: {
                    filename: 'assets/[name][ext]'
                }
            }
        ]
    },
    plugins: [
        new webpack.DefinePlugin({
            __VUE_OPTIONS_API__: true,
            __VUE_PROD_DEVTOOLS__: false
        }),
        new miniCssExtractPlugin({
            filename: './css/[name].css',
            ignoreOrder: true
        }),
        new VueLoaderPlugin.VueLoaderPlugin(),
        new CopyWebpackPlugin({
            patterns: [
                { from: 'images', to: 'images', noErrorOnMissing: true },
                { from: 'json', to: 'json', noErrorOnMissing: true }
            ],
            options: {
                concurrency: 100
            }
        })
    ],
    resolve: {
        alias: {
            'vue': 'vue/dist/vue.esm-bundler.js'
        }
    }
});