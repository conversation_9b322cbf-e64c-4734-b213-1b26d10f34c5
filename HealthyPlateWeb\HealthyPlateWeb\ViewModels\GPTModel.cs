﻿using System.Text.Json.Serialization;

namespace HealthyPlateWeb.ViewModels
{
    public class GPTModel
    {
        public string model { get; set; }
        public Message[] messages { get; set; }
        public Function[] functions { get; set; }
        public Function_Call function_call { get; set; }
        public int temperature { get; set; }
        public int max_tokens { get; set; }
        public int top_p { get; set; }
        public int frequency_penalty { get; set; }
        public int presence_penalty { get; set; }
    }

    public class Function_Call
    {
        public string name { get; set; }
    }

    public class Message
    {
        public string role { get; set; }
        public Content[] content { get; set; }
    }

    public class Content
    {
        public string type { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public Image_Url image_url { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string text { get; set; }
    }

    public class Image_Url
    {
        public string url { get; set; }
    }

    public class Function
    {
        public string name { get; set; }
        public Parameters parameters { get; set; }
    }

    public class Parameters
    {
        public string type { get; set; }
        public Properties properties { get; set; }
    }

    public class Properties
    {
        public Proportion proportion { get; set; }
    }

    public class Proportion
    {
        public string type { get; set; }
        public Items items { get; set; }
    }

    public class Items
    {
        public string type { get; set; }
        public Properties1 properties { get; set; }
        public string[] required { get; set; }
    }

    public class Properties1
    {
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public Item name { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public Item percentage { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public Item volume { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public Item density { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public Item weight { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public Item nutritional { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public Item nutritional_value { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public Item unit { get; set; }
    }

    public class Item
    {
        public string type { get; set; }
    }
}
