using HealthyPlateWeb.ViewModels;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Net.Http.Headers;
using System.Net.Mime;
using System.Text.Json;
using System.Text;
using System.Reflection;

namespace HealthyPlateWeb.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class CalcController(IHttpClientFactory clientFactory) : ControllerBase
    {
        private readonly string token = "********************************************************************************************************************************************************************"; 

        [HttpPost]
        public async Task<IActionResult> Measure(MeasureViewModel model)
        {
            model.Long = model.Long ?? 1;
            model.Width = model.Width ?? 1;
            model.High = model.High ?? 1;

            //動態替換{model.Long}公分，寬{model.Width}公分，高{model.High}變數
            model.PromptB = model.PromptB.Replace("{long}", model.Long.ToString()).Replace("{width}", model.Width.ToString()).Replace("{high}", model.High.ToString());

            var totalVolume = model.Long.Value * model.Width.Value * model.High.Value;
            var request = new HttpRequestMessage(HttpMethod.Post, "https://shinyilian.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview");
            //request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
            //add api-key
            request.Headers.Add("api-key", "********************************");
            var content = JsonSerializer.Serialize(new GPTModel()
            {
                model = "gpt-4o",
                messages = new[] {
                    new Message(){
                        role = "user",
                        content = new[]{
                            new Content(){
                                type = "image_url",
                                image_url = new Image_Url(){
                                    url= model.Img
                                }

                            }
                        }
                    },
                    new Message(){
                        role="assistant",
                        content = new[]{
                            new Content(){
                                type = "text",
                                text = model.PromptB
                                // text = $"這是個長{model.Long}公分，寬{model.Width}公分，高{model.High}公分的容器，識別圖片中容器內的食物名稱（如米飯、煎蛋等）及體積佔比，排除容器的部份僅輸出每種食物的名稱、體積(立方公分)、密度及重量(克)"
                                //text = @$"您將會看到一張圖片，該圖片顯示了一個透明的矩形容器，尺寸為長{model.Long}公分、寬{model.Width}公分、高{model.High}公分。
                                //            • 容器中有數種食材。請仔細觀察圖片，以辨識出容器中有哪些食材（例如米飯、煎蛋等），並盡可能正確指認它們。
                                //            • 在辨識食材後，請根據圖片描述（例如：某種食材填滿容器底部至一定高度、另一種食材在其上方等）來估計各該食材所佔的體積。若圖片中有明顯結構（如煎蛋是一片近似圓盤狀的物體），請嘗試以合理的近似方式計算其體積（如以圓柱體、長方體、球體或其他幾何形狀近似）。
                                //            • 為使計算可行，假設以下密度（若無法從圖片中得知食材密度，可先列出識別出的食物名稱，再在後面標註「未提供密度」或自行給出常見的平均密度值）：
                                //            • 米飯密度：1.5 g/cm³
                                //            • 煎蛋密度：1.3 g/cm³
                                //        （若您辨識到的食材並非上述兩種，請嘗試根據常識給出合理的平均密度值，或標明密度未知。）

                                //        任務階段：
                                //            1. 食材識別階段：請列出您在圖片中辨識到的所有食材名稱。
                                //            2. 體積計算階段：對於每項食材，根據其在容器中的位置與大致外型估算體積(cm³)。您可透過已知的容器尺寸、填滿比例或以合理的幾何近似方式計算。
                                //            3. 重量計算階段：利用提供或假設的密度，計算每種食材的重量(g)，公式為：重量(g) = 體積(cm³) × 密度(g/cm³)。

                                //        回答格式要求：
                                //        請最終僅輸出一個表格，格式如下（如果某項資料不確定，請註明「約略值」或「未知」）：

                                //        食物名稱	體積(cm³)	密度(g/cm³)	重量(g)
                                //        [食材1]	[計算值]	[假設值或已知值]	[計算值或約略值]
                                //        [食材2]	[計算值]	[假設值或已知值]	[計算值或約略值]

                                //        請只根據所觀察到的食材，以及上述計算原則提供答案，不要加入與圖片無關的推測。
                                //        "
                            }
                        }
                    }
                },
                functions = new[] {
                    new Function(){
                        name = "proportion",
                        parameters = new Parameters(){
                            type = "object",
                            properties = new Properties(){
                                proportion = new Proportion(){
                                    type = "array",
                                    items = new Items(){
                                        type = "object",
                                        properties = new Properties1(){
                                            name = new Item(){
                                                type = "string"
                                            },
                                            percentage = new Item(){
                                                type = "number"
                                            },
                                            volume = new Item(){
                                                type = "number"
                                            },
                                            density = new Item(){ 
                                                type= "number"
                                            },
                                            weight = new Item(){ 
                                                type = "number"
                                            }
                                        },
                                        required = new string[]{ "name", "percentage", "volume", "density", "weight" }
                                    }
                                }
                            }
                        }
                    }
                },
                function_call = new Function_Call()
                {
                    name = "proportion"
                },
                temperature = 1,
                max_tokens = 2048,
                top_p = 1,
                frequency_penalty = 0,
                presence_penalty = 0
            });
            request.Content = new StringContent(content, Encoding.UTF8, MediaTypeNames.Application.Json);
            var client = clientFactory.CreateClient();
            var response = await client.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var result = await JsonSerializer.DeserializeAsync<AiResponseViewModel>(await response.Content.ReadAsStreamAsync());
                var arg = JsonSerializer.Deserialize<AiAnlyzeProportionViewModel>(result.choices[0].message.function_call.arguments).proportion;
                var data = new List<AiAnalyzeResultViewModel>();
                foreach (var item in arg)
                {
                    var nutritional = await Nutritional(item.name,model.PromptA);
                    data.Add(new AiAnalyzeResultViewModel() { 
                        Name = item.name,
                        Percentage = item.percentage,
                        Volume = item.volume,
                        Density = item.density,
                        Weight = item.weight,
                        Nutritional = nutritional
                    });
                }
                return Ok(data);
            }
            else
            {
                return BadRequest(await response.Content.ReadAsStringAsync());
            }
        }

        public async Task<List<NutritionalViewModel>> Nutritional(string name,string prompt) {
            try
            {
                 // 動態替換{name}變數
                prompt = prompt.Replace("{name}", name);

                var request = new HttpRequestMessage(HttpMethod.Post, "https://api.openai.com/v1/chat/completions");
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                var content = JsonSerializer.Serialize(new GPTModel()
                {
                    model = "gpt-4o",
                    messages = new[] {
                    new Message(){
                        role="assistant",
                        content = new[]{
                            new Content(){
                                type = "text",
                                text = prompt
                                // text = $"用繁體中文回應我{name}的營養價值大約是多少"
                                // text = $@"
                                //     請列出以下各種食材{name}在每100克中常見的平均營養價值，包括：

                                //     熱量(大卡)
                                //     碳水化合物(g)
                                //     蛋白質(g)
                                //     膳食纖維(g)
                                //     鈣質(mg)
                                //     鐵質(mg)
                                //     葉酸(µg)

                                //     若該食材幾乎不含某項營養素，請在該欄位填入0。
                                //     請以下列表格格式呈現結果：

                                //     食材	熱量(大卡/100g)	碳水化合物(g/100g)	蛋白質(g/100g)	膳食纖維(g/100g)	鈣質(mg/100g)	鐵質(mg/100g)	葉酸(µg/100g)

                                //     請僅依上述要求依序提供資訊，不需額外說明。將 temperature 設為 0。
                                //     "

                            }
                        }
                    }
                },
                    functions = new[] {
                    new Function(){
                        name = "nutritional",
                        parameters = new Parameters(){
                            type = "object",
                            properties = new Properties(){
                                proportion = new Proportion(){
                                    type = "array",
                                    items = new Items(){
                                        type = "object",
                                        properties = new Properties1(){
                                            nutritional = new Item(){
                                                type = "string"
                                            },
                                            nutritional_value = new Item(){
                                                type = "number"
                                            },
                                            unit  = new Item(){
                                                type = "string"
                                            },
                                        },
                                        required = new string[]{ "nutritional", "nutritional_value", "unit" }
                                    }
                                }
                            }
                        }
                    }
                },
                    function_call = new Function_Call()
                    {
                        name = "nutritional"
                    },
                    temperature = 1,
                    max_tokens = 2048,
                    top_p = 1,
                    frequency_penalty = 0,
                    presence_penalty = 0
                });
                request.Content = new StringContent(content, Encoding.UTF8, MediaTypeNames.Application.Json);
                var client = clientFactory.CreateClient();
                var response = await client.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var jsonStr = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<AiResponseViewModel>(jsonStr);
                    var arg = JsonSerializer.Deserialize<NutritionalRoot>(result.choices[0].message.function_call.arguments);
                    return arg.proportion.ToList();
                }
                else
                {
                    return new List<NutritionalViewModel>();
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            
        }
    }
}
