{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "AIModels": {"DefaultModel": "azure-gpt4o", "Models": {"azure-gpt4o": {"Name": "Azure GPT-4o", "Provider": "AzureOpenAI", "ModelName": "gpt-4o", "BaseUrl": "https://shinyilian.openai.azure.com/openai/deployments/gpt-4o/chat/completions", "ApiVersion": "2025-01-01-preview", "MaxTokens": 2048, "Temperature": 1, "IsEnabled": true}, "openai-gpt4o": {"Name": "OpenAI GPT-4o", "Provider": "OpenAI", "ModelName": "gpt-4o", "BaseUrl": "https://api.openai.com/v1/chat/completions", "MaxTokens": 2048, "Temperature": 1, "IsEnabled": true}, "azure-gpt35": {"Name": "Azure GPT-3.5 Turbo", "Provider": "AzureOpenAI", "ModelName": "gpt-35-turbo", "BaseUrl": "https://shinyilian.openai.azure.com/openai/deployments/gpt-35-turbo/chat/completions", "ApiVersion": "2025-01-01-preview", "MaxTokens": 2048, "Temperature": 1, "IsEnabled": false}}}}