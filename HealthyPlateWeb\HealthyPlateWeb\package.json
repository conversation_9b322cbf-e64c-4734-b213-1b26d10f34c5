{"version": "1.0.0", "name": "asp.net", "private": true, "scripts": {"dev": "webpack --config webpack.dev.js --watch", "prod": "webpack --config webpack.prod.js"}, "devDependencies": {"@babel/core": "7.26.0", "@babel/plugin-transform-runtime": "7.25.9", "@babel/preset-env": "7.26.0", "@babel/runtime": "7.26.0", "babel-loader": "9.2.1", "copy-webpack-plugin": "12.0.2", "css-loader": "7.1.2", "mini-css-extract-plugin": "2.9.2", "postcss-loader": "8.1.1", "sass": "1.80.6", "sass-loader": "16.0.3", "vue-loader": "17.4.2", "webpack": "5.96.1", "webpack-cli": "5.1.4", "webpack-merge": "6.0.1"}, "dependencies": {"bootstrap": "5.3.3", "vue": "3.5.12", "axios": "1.7.7", "sweetalert2": "11.14.5", "toastr": "2.1.4", "three": "0.125.2", "webxr-polyfill": "2.0.3", "vue-loading-overlay": "6.0.6"}, "babel": {"presets": ["@babel/preset-env"]}}