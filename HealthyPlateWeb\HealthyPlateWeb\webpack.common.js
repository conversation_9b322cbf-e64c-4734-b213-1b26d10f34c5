﻿"use strict";
const path = require('path');

module.exports = {
    context: path.resolve(__dirname, 'src'),
    entry: {
        Index: "./js/page/Index.js"
    },
    output: {
        path: path.resolve(process.cwd(), 'wwwroot/dist'),
        filename: "./js/[name].js"
    },
    optimization: {
        splitChunks: {
            cacheGroups: {
                vendor: {
                    test: /node_modules/,
                    chunks: 'initial',
                    minChunks: 5,
                    name: 'vendor'
                }
            }
        }
    }
};