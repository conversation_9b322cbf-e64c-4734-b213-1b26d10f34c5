import '../../scss/index.scss';
import { createApp, ref, onMounted } from 'vue';

import * as THREE from 'three';
import { BufferGeometryUtils } from 'three/examples/jsm/utils/BufferGeometryUtils.js';
import axios from 'axios';
import { useLoading } from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';

let container, labelContainer;
let camera, scene, renderer, light;
let controller;

let hitTestSource = null;
let hitTestSourceRequested = false;

let measurements = [];
let labels = [];

let reticle;
let currentLine = null;

let width, height;


function matrixToVector(matrix) {
    let vector = new THREE.Vector3();
    vector.setFromMatrixPosition(matrix);
    return vector;
}

function initLine(point) {
    let lineMaterial = new THREE.LineBasicMaterial({
        color: 0xffffff,
        linewidth: 5,
        linecap: 'round'
    });

    let lineGeometry = new THREE.BufferGeometry().setFromPoints([point, point]);
    return new THREE.Line(lineGeometry, lineMaterial);
}

function updateLine(matrix) {
    let positions = currentLine.geometry.attributes.position.array;
    positions[3] = matrix.elements[12]
    positions[4] = matrix.elements[13]
    positions[5] = matrix.elements[14]
    currentLine.geometry.attributes.position.needsUpdate = true;
    currentLine.geometry.computeBoundingSphere();
}

function initReticle() {
    let ring = new THREE.RingBufferGeometry(0.045, 0.05, 32).rotateX(- Math.PI / 2);
    let dot = new THREE.CircleBufferGeometry(0.005, 32).rotateX(- Math.PI / 2);
    reticle = new THREE.Mesh(
        BufferGeometryUtils.mergeBufferGeometries([ring, dot]),
        new THREE.MeshBasicMaterial()
    );
    reticle.matrixAutoUpdate = false;
    reticle.visible = false;
}
function initRenderer() {
    renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true
    });
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.xr.enabled = true;
}

function initLabelContainer() {
    labelContainer = document.createElement('div');
    labelContainer.style.position = 'absolute';
    labelContainer.style.top = '0px';
    labelContainer.style.pointerEvents = 'none';
    labelContainer.setAttribute('id', 'container');
}

function initCamera() {
    camera = new THREE.PerspectiveCamera(70, width / height, 0.01, 20);
}

function initLight() {
    light = new THREE.HemisphereLight(0xffffff, 0xbbbbff, 1);
    light.position.set(0.5, 1, 0.25);
}

function initScene() {
    scene = new THREE.Scene();
}

function getDistance(points) {
    if (points.length == 2)
        return points[0].distanceTo(points[1]);
}

function initXR() {
    container = document.createElement('div');
    document.body.appendChild(container);

    width = window.innerWidth;
    height = window.innerHeight;

    initScene();

    initCamera();

    initLight();
    scene.add(light);

    initRenderer()
    container.appendChild(renderer.domElement);

    controller = renderer.xr.getController(0);
    controller.addEventListener('select', onSelect);
    scene.add(controller);

    initReticle();
    scene.add(reticle);
    animate()
}

function onSelect() {
    if (reticle.visible) {
        measurements.push(matrixToVector(reticle.matrix));
        if (measurements.length == 2) {
            let distance = Math.round(getDistance(measurements) * 10000) / 100;
            //data.value = distance + ' cm';

            switch (currentType.value) {
                case "long":
                    longData.value = distance;
                    break;
                case "width":
                    widthData.value = distance;
                    break;
                case "high":
                    highData.value = distance;
                    break;
                default:
                    break;
            }

            measurements = [];
            currentLine = null;

            let xrSession = renderer.xr.getSession();
            xrSession.end();
            xrSession = null;
            document.getElementById("app").style.display = "";
            document.getElementById("ARButton").style.display = "";
        } else {
            currentLine = initLine(measurements[0]);
            scene.add(currentLine);
        }
    }
}

function animate() {
    renderer.setAnimationLoop(render);
}

function render(timestamp, frame) {
    if (frame) {
        let referenceSpace = renderer.xr.getReferenceSpace();
        let session = renderer.xr.getSession();
        if (hitTestSourceRequested === false) {
            session.requestReferenceSpace('viewer').then(function (referenceSpace) {
                session.requestHitTestSource({ space: referenceSpace }).then(function (source) {
                    hitTestSource = source;
                });
            });
            session.addEventListener('end', function () {
                hitTestSourceRequested = false;
                hitTestSource = null;
            });
            hitTestSourceRequested = true;
        }

        if (hitTestSource) {
            let hitTestResults = frame.getHitTestResults(hitTestSource);
            if (hitTestResults.length) {
                let hit = hitTestResults[0];
                reticle.visible = true;
                reticle.matrix.fromArray(hit.getPose(referenceSpace).transform.matrix);
            } else {
                reticle.visible = false;
            }

            if (currentLine) {
                updateLine(reticle.matrix);
            }
        }
    }
    renderer.render(scene, camera);

}

initXR();

let xrSession = null;

const currentType = ref("");
const longData = ref("");
const widthData = ref("");
const highData = ref("");
const promptA = ref("");
const promptB = ref("");

const StartAR = async (type) => {
    currentType.value = type;
    const session = await navigator.xr.requestSession('immersive-ar');
    xrSession = session;
    renderer.xr.setReferenceSpaceType('local');
    await renderer.xr.setSession(session);

    xrSession.onend = function () {
        xrSession.end();
        xrSession = null;

        document.getElementById('app').style.display = 'inline';
    };
    document.getElementById('app').style.display = 'none';
}

const toBase64 = file => new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
});
const $loading = useLoading({
    //// 指定選項
    //color: 'blue',                  // loading畫面的顏色
    //backgroundColor: 'white',       // 背景顏色
    //opacity: 0.8,                   // 透明度
    //blur: '10px',                   // 背景模糊效果
    zIndex: 9999                    // loading畫面的層級
});
const Analyze = async () => {
    const loader = $loading.show();
    const file = document.querySelector('#file').files[0];
    const fileBase64Str = await toBase64(file);
    axios.post("/api/Calc/Measure", {
        Long: longData.value,
        Width: widthData.value,
        High: highData.value,
        Img: fileBase64Str,
        promptA: promptA.value,
        promptB: promptB.value,
    }).then(function (result) {
        AnalyzeResult.value = result.data;
    }).catch((error) => {
        alert(error);
    }).finally(() => {
        loader.hide();
    })
}

const SetDefault = () => {
    longData.value = "16";
    widthData.value = "10";
    highData.value = "3";
    promptA.value = "請列出以下各種食材{name}在每100克中常見的平均營養價值，包括：\n\n熱量(大卡)\n碳水化合物(g)\n蛋白質(g)\n膳食纖維(g)\n鈣質(mg)\n鐵質(mg)\n葉酸(µg)\n\n若該食材幾乎不含某項營養素，請在該欄位填入0。\n請以下列表格格式呈現結果：\n\n食材\t熱量(大卡/100g)\t碳水化合物(g/100g)\t蛋白質(g/100g)\t膳食纖維(g/100g)\t鈣質(mg/100g)\t鐵質(mg/100g)\t葉酸(µg/100g)\n\n請僅依上述要求依序提供資訊，不需額外說明。將 temperature 設為 0。";
    promptB.value = "您將會看到一張圖片，該圖片顯示了一個透明的矩形容器，尺寸為長{long}公分、寬{width}公分、高{high}公分。 • 容器中有數種食材。請仔細觀察圖片，以辨識出容器中有哪些食材（例如米飯、煎蛋等），並盡可能正確指認它們。 • 在辨識食材後，請根據圖片描述（例如：某種食材填滿容器底部至一定高度、另一種食材在其上方等）來估計各該食材所佔的體積。若圖片中有明顯結構（如煎蛋是一片近似圓盤狀的物體），請嘗試以合理的近似方式計算其體積（如以圓柱體、長方體、球體或其他幾何形狀近似）。 • 為使計算可行，假設以下密度（若無法從圖片中得知食材密度，可先列出識別出的食物名稱，再在後面標註「未提供密度」或自行給出常見的平均密度值）： • 米飯密度：1.5 g/cm³ • 煎蛋密度：1.3 g/cm³ （若您辨識到的食材並非上述兩種，請嘗試根據常識給出合理的平均密度值，或標明密度未知。） 任務階段： 1. 食材識別階段：請列出您在圖片中辨識到的所有食材名稱。 2. 體積計算階段：對於每項食材，根據其在容器中的位置與大致外型估算體積(cm³)。您可透過已知的容器尺寸、填滿比例或以合理的幾何近似方式計算。 3. 重量計算階段：利用提供或假設的密度，計算每種食材的重量(g)，公式為：重量(g) = 體積(cm³) × 密度(g/cm³)。 回答格式要求：請最終僅輸出一個表格，格式如下（如果某項資料不確定，請註明「約略值」或「未知」）： 食物名稱 體積(cm³) 密度(g/cm³) 重量(g) [食材1] [計算值] [假設值或已知值] [計算值或約略值] [食材2] [計算值] [假設值或已知值] [計算值或約略值] 請只根據所觀察到的食材，以及上述計算原則提供答案，不要加入與圖片無關的推測。";
}

const AnalyzeResult = ref([]);
const App = {
    setup() {
        return {
            currentType,
            longData,
            widthData,
            highData,
            promptA,
            promptB,
            StartAR,
            Analyze,
            SetDefault,
            AnalyzeResult
        }
    }
}
createApp(App).mount('#app');