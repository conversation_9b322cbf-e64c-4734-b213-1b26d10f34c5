﻿using System.Text.Json.Serialization;

namespace HealthyPlateWeb.ViewModels
{
    public class AiResponseViewModel
    {
        public string id { get; set; }
        public string _object { get; set; }
        public int created { get; set; }
        public string model { get; set; }
        public Choice[] choices { get; set; }
        public Usage usage { get; set; }
        public string system_fingerprint { get; set; }
    }

    public class Usage
    {
        public int prompt_tokens { get; set; }
        public int completion_tokens { get; set; }
        public int total_tokens { get; set; }
        public Prompt_Tokens_Details prompt_tokens_details { get; set; }
        public Completion_Tokens_Details completion_tokens_details { get; set; }
    }

    public class Prompt_Tokens_Details
    {
        public int cached_tokens { get; set; }
        public int audio_tokens { get; set; }
    }

    public class Completion_Tokens_Details
    {
        public int reasoning_tokens { get; set; }
        public int audio_tokens { get; set; }
        public int accepted_prediction_tokens { get; set; }
        public int rejected_prediction_tokens { get; set; }
    }

    public class Choice
    {
        public int index { get; set; }
        public ResponseMessage message { get; set; }
        public object logprobs { get; set; }
        public string finish_reason { get; set; }
    }

    public class ResponseMessage
    {
        public string role { get; set; }
        public object content { get; set; }
        public ResponseFunction_Call function_call { get; set; }
        public object refusal { get; set; }
    }

    public class ResponseFunction_Call
    {
        public string name { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string arguments { get; set; }
    }

}
