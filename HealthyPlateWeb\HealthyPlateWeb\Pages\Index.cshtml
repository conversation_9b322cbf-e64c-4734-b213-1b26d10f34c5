@page
@model HealthyPlateWeb.Pages.IndexModel
@{
}
@section Styles {
   <link href="~/dist/css/Index.css" rel="stylesheet" />
}
@section Scripts {
    <script src="/dist/js/Index.js"></script>
}
<div id="app" v-cloak>
    <input type="text" v-model="longData"/> cm
    <button @@click="StartAR('long')">AR測量長度</button>
    <input type="text" v-model="widthData" /> cm
    <button @@click="StartAR('width')">AR測量寬度</button>
    <input type="text" v-model="highData" /> cm
    <button @@click="StartAR('high')">AR測量高度</button>
      <br>
    <input id="file" type="file" />
      <br>
    <button @@click="SetDefault">帶入預設</button>  <br>
    <button @@click="Analyze">開始分析</button>
     <br>
    <textarea id="promptB" v-model="promptB" rows="20" cols="100"></textarea>
    <textarea id="promptA" v-model="promptA" rows="20" cols="100"></textarea>
    <template v-if="AnalyzeResult.length>0">
        <h2>結果</h2>
        <table>
            <tr>
                <th>成份</th>
                <th>佔比</th>
                <th>估計體積</th>
                <th>估計密度</th>
                <th>估計重量</th>
                <th>營養</th>
            </tr>
            <tr v-for="item in AnalyzeResult" :key="name">
                <td>
                    {{item.name}}
                </td>
                <td>
                    {{item.percentage}}
                </td>
                <td>
                    {{item.volume}}
                </td>
                <td>
                    {{item.density}}
                </td>
                <td>
                    {{item.weight}}克
                </td>
                <td>
                    <table>
                        <tr v-for="n in item.nutritional">
                            <td>{{n.nutritional}}</td>
                            <td>{{(n.nutritional_value*item.density*item.weight/100).toFixed(2)}}</td>
                            <td>{{n.unit}}</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </template>
</div>